"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  BarChart3,
  TrendingUp,
  Download,
  Calendar,
  Users,
  FileText,
  PieChart,
  Activity,
  Clock,
  UserCheck,
  UserX,
  AlertTriangle,
  Target,
} from "lucide-react"
import { useRTL } from "@/contexts/rtl-context"
import { useAppStore } from "@/stores/app-store"
import { cn } from "@/lib/utils"
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, subWeeks, subMonths } from "date-fns"
import { ar } from "date-fns/locale"
import AppLayout from "@/layouts/app-layout"

function ReportsPage() {
  const { users, attendanceRecords } = useAppStore()
  const { isRTL, direction } = useRTL()

  const [dateFrom, setDateFrom] = useState(format(subDays(new Date(), 30), "yyyy-MM-dd"))
  const [dateTo, setDateTo] = useState(format(new Date(), "yyyy-MM-dd"))
  const [selectedYear, setSelectedYear] = useState("all")
  const [selectedCollege, setSelectedCollege] = useState("all")
  const [reportType, setReportType] = useState("summary")

  const today = new Date()
  const colleges = [...new Set(users.map((u) => u.college))]

  // Filter data based on selections
  const filteredUsers = useMemo(() => {
    return users.filter((user) => {
      const matchesYear = selectedYear === "all" || user.year.toString() === selectedYear
      const matchesCollege = selectedCollege === "all" || user.college === selectedCollege
      return matchesYear && matchesCollege
    })
  }, [users, selectedYear, selectedCollege])

  const filteredAttendance = useMemo(() => {
    const fromDate = new Date(dateFrom)
    const toDate = new Date(dateTo)

    return attendanceRecords.filter((record) => {
      const recordDate = new Date(record.date)
      const matchesUser = filteredUsers.some((user) => user.id === record.user_id)
      const matchesDateRange = recordDate >= fromDate && recordDate <= toDate
      return matchesUser && matchesDateRange
    })
  }, [attendanceRecords, filteredUsers, dateFrom, dateTo])

  // Calculate statistics
  const reportStats = useMemo(() => {
    const totalUsers = filteredUsers.length
    const totalSessions = new Set(filteredAttendance.map((r) => r.date)).size
    const totalAttendanceRecords = filteredAttendance.length
    const presentCount = filteredAttendance.filter((r) => r.present).length
    const absentCount = totalAttendanceRecords - presentCount
    const attendanceRate = totalAttendanceRecords > 0 ? Math.round((presentCount / totalAttendanceRecords) * 100) : 0

    // Calculate user-specific stats
    const userStats = filteredUsers.map((user) => {
      const userAttendance = filteredAttendance.filter((r) => r.user_id === user.id)
      const userPresent = userAttendance.filter((r) => r.present).length
      const userAbsent = userAttendance.length - userPresent
      const userRate = userAttendance.length > 0 ? Math.round((userPresent / userAttendance.length) * 100) : 0

      return {
        ...user,
        totalSessions: userAttendance.length,
        presentSessions: userPresent,
        absentSessions: userAbsent,
        attendanceRate: userRate,
      }
    })

    // Sort by attendance rate
    userStats.sort((a, b) => b.attendanceRate - a.attendanceRate)

    // Calculate college stats
    const collegeStats = colleges.map((college) => {
      const collegeUsers = filteredUsers.filter((u) => u.college === college)
      const collegeAttendance = filteredAttendance.filter((r) =>
        collegeUsers.some((u) => u.id === r.user_id)
      )
      const collegePresent = collegeAttendance.filter((r) => r.present).length
      const collegeRate = collegeAttendance.length > 0 ? Math.round((collegePresent / collegeAttendance.length) * 100) : 0

      return {
        college,
        totalUsers: collegeUsers.length,
        totalAttendance: collegeAttendance.length,
        presentCount: collegePresent,
        attendanceRate: collegeRate,
      }
    })

    return {
      totalUsers,
      totalSessions,
      presentCount,
      absentCount,
      attendanceRate,
      userStats,
      collegeStats,
    }
  }, [filteredUsers, filteredAttendance, colleges])

  // Get weekly attendance trend
  const weeklyTrend = useMemo(() => {
    const weeks = []
    const startDate = new Date(dateFrom)
    const endDate = new Date(dateTo)

    let currentWeek = startOfWeek(startDate, { weekStartsOn: 1 })

    while (currentWeek <= endDate) {
      const weekEnd = endOfWeek(currentWeek, { weekStartsOn: 1 })
      const weekAttendance = filteredAttendance.filter((record) => {
        const recordDate = new Date(record.date)
        return recordDate >= currentWeek && recordDate <= weekEnd
      })

      const weekPresent = weekAttendance.filter((r) => r.present).length
      const weekTotal = weekAttendance.length
      const weekRate = weekTotal > 0 ? Math.round((weekPresent / weekTotal) * 100) : 0

      weeks.push({
        week: format(currentWeek, "dd MMM", { locale: ar }),
        presentCount: weekPresent,
        totalCount: weekTotal,
        attendanceRate: weekRate,
      })

      currentWeek = new Date(currentWeek.getTime() + 7 * 24 * 60 * 60 * 1000)
    }

    return weeks
  }, [filteredAttendance, dateFrom, dateTo])

  const exportReport = (format: "pdf" | "csv") => {
    if (format === "csv") {
      let csvContent = ""

      if (reportType === "summary") {
        csvContent = [
          ["التقرير الإجمالي", "", "", ""],
          ["الفترة", `${dateFrom} إلى ${dateTo}`, "", ""],
          ["", "", "", ""],
          ["إجمالي الأعضاء", reportStats.totalUsers, "", ""],
          ["إجمالي الجلسات", reportStats.totalSessions, "", ""],
          ["إجمالي الحضور", reportStats.presentCount, "", ""],
          ["إجمالي الغياب", reportStats.absentCount, "", ""],
          ["معدل الحضور", `${reportStats.attendanceRate}%`, "", ""],
          ["", "", "", ""],
          ["الاسم", "إجمالي الجلسات", "الحضور", "معدل الحضور"],
          ...reportStats.userStats.map((user) => [
            user.name,
            user.totalSessions,
            user.presentSessions,
            `${user.attendanceRate}%`,
          ]),
        ]
          .map((row) => row.join(","))
          .join("\n")
      }

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
      const link = document.createElement("a")
      link.href = URL.createObjectURL(blob)
      link.download = `attendance-report-${format(today, "yyyy-MM-dd")}.csv`
      link.click()
    }
  }

  const getQuickDateRange = (range: string) => {
    const today = new Date()

    switch (range) {
      case "today":
        setDateFrom(format(today, "yyyy-MM-dd"))
        setDateTo(format(today, "yyyy-MM-dd"))
        break
      case "week":
        setDateFrom(format(startOfWeek(today, { weekStartsOn: 1 }), "yyyy-MM-dd"))
        setDateTo(format(endOfWeek(today, { weekStartsOn: 1 }), "yyyy-MM-dd"))
        break
      case "month":
        setDateFrom(format(startOfMonth(today), "yyyy-MM-dd"))
        setDateTo(format(endOfMonth(today), "yyyy-MM-dd"))
        break
      case "last7":
        setDateFrom(format(subDays(today, 6), "yyyy-MM-dd"))
        setDateTo(format(today, "yyyy-MM-dd"))
        break
      case "last30":
        setDateFrom(format(subDays(today, 29), "yyyy-MM-dd"))
        setDateTo(format(today, "yyyy-MM-dd"))
        break
    }
  }

  return (
    <div className="p-6 space-y-6 font-cairo" dir={direction}>
      {/* Header */}
      <div className="animate-fade-in">
        <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
          <div>
            <div className={cn("flex items-center gap-3 mb-2", isRTL ? "flex-row-reverse" : "flex-row")}>
              <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-heading gradient-text">التقارير والإحصائيات</h1>
              <TrendingUp className="h-6 w-6 text-blue-500 animate-pulse" />
            </div>
            <p className={cn("text-gray-600 text-lg text-body", isRTL ? "text-right" : "text-left")}>
              تحليل وتقارير شاملة لحضور الأعضاء
            </p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={() => exportReport("csv")}>
              <Download className="h-4 w-4 mr-2" />
              تصدير CSV
            </Button>
            <Button variant="outline" onClick={() => exportReport("pdf")}>
              <FileText className="h-4 w-4 mr-2" />
              تصدير PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="animate-scale-in">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            إعدادات التقرير
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">من تاريخ</label>
              <Input
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">إلى تاريخ</label>
              <Input
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">السنة</label>
              <Select value={selectedYear} onValueChange={setSelectedYear}>
                <SelectTrigger>
                  <SelectValue placeholder="السنة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع السنوات</SelectItem>
                  <SelectItem value="1">السنة الأولى</SelectItem>
                  <SelectItem value="2">السنة الثانية</SelectItem>
                  <SelectItem value="3">السنة الثالثة</SelectItem>
                  <SelectItem value="4">السنة الرابعة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">الكلية</label>
              <Select value={selectedCollege} onValueChange={setSelectedCollege}>
                <SelectTrigger>
                  <SelectValue placeholder="الكلية" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الكليات</SelectItem>
                  {colleges.map((college) => (
                    <SelectItem key={college} value={college}>
                      {college}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">نوع التقرير</label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue placeholder="نوع التقرير" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">تقرير إجمالي</SelectItem>
                  <SelectItem value="detailed">تقرير تفصيلي</SelectItem>
                  <SelectItem value="trends">اتجاهات الحضور</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">فترات سريعة</label>
              <Select onValueChange={getQuickDateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر فترة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">اليوم</SelectItem>
                  <SelectItem value="week">هذا الأسبوع</SelectItem>
                  <SelectItem value="month">هذا الشهر</SelectItem>
                  <SelectItem value="last7">آخر 7 أيام</SelectItem>
                  <SelectItem value="last30">آخر 30 يوم</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 animate-scale-in">
        <Card className="hover-lift">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الأعضاء</p>
                <p className="text-2xl font-bold">{reportStats.totalUsers}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الجلسات</p>
                <p className="text-2xl font-bold">{reportStats.totalSessions}</p>
              </div>
              <Calendar className="h-8 w-8 text-indigo-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الحضور</p>
                <p className="text-2xl font-bold text-green-600">{reportStats.presentCount}</p>
              </div>
              <UserCheck className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الغياب</p>
                <p className="text-2xl font-bold text-red-600">{reportStats.absentCount}</p>
              </div>
              <UserX className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">معدل الحضور</p>
                <p className="text-2xl font-bold text-blue-600">{reportStats.attendanceRate}%</p>
              </div>
              <Target className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Content */}
      <Card className="animate-slide-in-right">
        <CardContent className="p-0">
          <Tabs value={reportType} onValueChange={setReportType} className="w-full">
            <div className="p-6 pb-0">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="summary">التقرير الإجمالي</TabsTrigger>
                <TabsTrigger value="detailed">التقرير التفصيلي</TabsTrigger>
                <TabsTrigger value="trends">اتجاهات الحضور</TabsTrigger>
              </TabsList>
            </div>

            {/* Summary Report */}
            <TabsContent value="summary" className="p-6 pt-4">
              <div className="space-y-6">
                {/* College Statistics */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">إحصائيات الكليات</h3>
                  <div className="grid gap-4">
                    {reportStats.collegeStats.map((college) => (
                      <Card key={college.college} className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{college.college}</h4>
                            <p className="text-sm text-gray-600">{college.totalUsers} طالب</p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold">{college.attendanceRate}%</div>
                            <Badge variant={college.attendanceRate >= 80 ? "default" : college.attendanceRate >= 60 ? "secondary" : "destructive"}>
                              {college.presentCount} / {college.totalAttendance}
                            </Badge>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Top Performers */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">أفضل 10 في الحضور</h3>
                  <div className="space-y-3">
                    {reportStats.userStats.slice(0, 10).map((user, index) => (
                      <div key={user.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-bold">{index + 1}</span>
                          </div>
                          <div>
                            <div className="font-medium">{user.name}</div>
                            <div className="text-sm text-gray-600">السنة {user.year} - {user.college}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-lg">{user.attendanceRate}%</div>
                          <div className="text-sm text-gray-600">{user.presentSessions} / {user.totalSessions}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Detailed Report */}
            <TabsContent value="detailed" className="p-6 pt-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">تقرير تفصيلي لجميع الأعضاء</h3>
                  <Badge variant="outline">{reportStats.userStats.length} عضو</Badge>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-200">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-200 p-3 text-right">الترتيب</th>
                        <th className="border border-gray-200 p-3 text-right">الاسم</th>
                        <th className="border border-gray-200 p-3 text-right">السنة</th>
                        <th className="border border-gray-200 p-3 text-right">الكلية</th>
                        <th className="border border-gray-200 p-3 text-right">الجلسات</th>
                        <th className="border border-gray-200 p-3 text-right">الحضور</th>
                        <th className="border border-gray-200 p-3 text-right">الغياب</th>
                        <th className="border border-gray-200 p-3 text-right">المعدل</th>
                        <th className="border border-gray-200 p-3 text-right">الحالة</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportStats.userStats.map((user, index) => (
                        <tr key={user.id} className="hover:bg-gray-50">
                          <td className="border border-gray-200 p-3">{index + 1}</td>
                          <td className="border border-gray-200 p-3 font-medium">{user.name}</td>
                          <td className="border border-gray-200 p-3">{user.year}</td>
                          <td className="border border-gray-200 p-3">{user.college}</td>
                          <td className="border border-gray-200 p-3">{user.totalSessions}</td>
                          <td className="border border-gray-200 p-3 text-green-600">{user.presentSessions}</td>
                          <td className="border border-gray-200 p-3 text-red-600">{user.absentSessions}</td>
                          <td className="border border-gray-200 p-3 font-bold">{user.attendanceRate}%</td>
                          <td className="border border-gray-200 p-3">
                            <Badge variant={
                              user.attendanceRate >= 90 ? "default" :
                              user.attendanceRate >= 70 ? "secondary" :
                              user.attendanceRate >= 50 ? "outline" : "destructive"
                            }>
                              {user.attendanceRate >= 90 ? "ممتاز" :
                               user.attendanceRate >= 70 ? "جيد" :
                               user.attendanceRate >= 50 ? "مقبول" : "ضعيف"}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>

            {/* Trends Report */}
            <TabsContent value="trends" className="p-6 pt-4">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">اتجاه الحضور الأسبوعي</h3>
                  <div className="space-y-4">
                    {weeklyTrend.map((week, index) => (
                      <div key={index} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                        <div className="w-20 text-sm font-medium">{week.week}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="text-sm text-gray-600">الحضور: {week.presentCount}</div>
                            <div className="text-sm text-gray-600">الإجمالي: {week.totalCount}</div>
                            <div className="text-sm font-medium">{week.attendanceRate}%</div>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${week.attendanceRate}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Performance Analysis */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">تحليل الأداء</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="p-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {reportStats.userStats.filter(u => u.attendanceRate >= 80).length}
                        </div>
                        <div className="text-sm text-gray-600">أعضاء ممتازون (80%+)</div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600">
                          {reportStats.userStats.filter(u => u.attendanceRate >= 60 && u.attendanceRate < 80).length}
                        </div>
                        <div className="text-sm text-gray-600">أعضاء جيدون (60-79%)</div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">
                          {reportStats.userStats.filter(u => u.attendanceRate < 60).length}
                        </div>
                        <div className="text-sm text-gray-600">يحتاجون متابعة (60%)</div>
                      </div>
                    </Card>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

ReportsPage.layout = (page: React.ReactElement) => <AppLayout children={page} />

export default ReportsPage
